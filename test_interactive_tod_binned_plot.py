#!/usr/bin/env python3
"""
Test script to demonstrate the interactive ToD binned plot functionality.
This script creates sample data and shows the interactive plot.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from visualizations.tod_tab_visual import create_tod_binned_plot_interactive

def create_sample_data():
    """
    Create sample ToD data for testing the interactive plot.
    """
    # Define ToD slots
    slots = ['Slot1', 'Slot2', 'Slot3', 'Slot4']
    
    # Create sample data with realistic values
    np.random.seed(42)  # For reproducible results
    
    data = []
    for slot in slots:
        # Generate realistic generation and consumption values
        if slot == 'Slot1':  # Peak hours - higher consumption
            generation = np.random.uniform(800, 1200)
            consumption = np.random.uniform(1000, 1500)
        elif slot == 'Slot2':  # Off-peak - lower values
            generation = np.random.uniform(400, 600)
            consumption = np.random.uniform(300, 500)
        elif slot == 'Slot3':  # Normal hours
            generation = np.random.uniform(600, 900)
            consumption = np.random.uniform(700, 1000)
        else:  # Slot4 - Solar peak
            generation = np.random.uniform(1000, 1400)
            consumption = np.random.uniform(600, 800)
        
        data.append({
            'slot': slot,
            'generation_kwh': generation,
            'consumption_kwh': consumption
        })
    
    return pd.DataFrame(data)

def main():
    """
    Main function to create and display the interactive plot.
    """
    print("Creating sample ToD data...")
    df = create_sample_data()
    
    print("Sample data:")
    print(df.round(2))
    print()
    
    # Create the interactive plot
    print("Creating interactive ToD binned plot...")
    
    try:
        fig = create_tod_binned_plot_interactive(
            df=df,
            plant_name="Sample Solar Plant",
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        
        print("Interactive plot created successfully!")
        print("Opening plot in browser...")
        
        # Show the plot in browser
        fig.show()
        
        # Optionally save as HTML
        output_file = "tod_binned_plot_interactive.html"
        fig.write_html(output_file)
        print(f"Plot saved as: {output_file}")
        
        # Print some information about the plot
        print("\nPlot Features:")
        print("- Interactive hover tooltips showing detailed information")
        print("- Slot time information in hover")
        print("- Values displayed in both kWh and MWh")
        print("- Grouped bar chart with generation and consumption")
        print("- Professional styling with proper colors")
        print("- Responsive layout")
        
    except Exception as e:
        print(f"Error creating plot: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
